import pyautogui
import time
import os
import json
import random
from typing import Dict, Tu<PERSON>, Optional


class ControlClicker:
    """控件点击器类 - 用于管理和点击页面控件"""

    def __init__(self, config_file: str = "controls_config.json"):
        """
        初始化控件点击器

        Args:
            config_file: 控件配置文件路径
        """
        self.config_file = config_file
        self.controls = {}
        self.load_controls_config()

    def load_controls_config(self) -> None:
        """从配置文件加载控件范围信息"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.controls = json.load(f)
                print(f"已加载 {len(self.controls)} 个控件配置")
            else:
                print(f"配置文件 {self.config_file} 不存在，将创建新的配置")
                self.controls = {}
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            self.controls = {}

    def save_controls_config(self) -> None:
        """保存控件配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.controls, f, ensure_ascii=False, indent=2)
            print(f"配置已保存到 {self.config_file}")
        except Exception as e:
            print(f"保存配置文件失败: {e}")

    def add_control(self, name: str, x1: int, y1: int, x2: int, y2: int, description: str = "") -> None:
        """
        添加控件范围

        Args:
            name: 控件名称
            x1, y1: 左上角坐标
            x2, y2: 右下角坐标
            description: 控件描述
        """
        # 确保坐标顺序正确
        min_x, max_x = min(x1, x2), max(x1, x2)
        min_y, max_y = min(y1, y2), max(y1, y2)

        self.controls[name] = {
            "x1": min_x,
            "y1": min_y,
            "x2": max_x,
            "y2": max_y,
            "description": description
        }
        print(f"已添加控件 '{name}': ({min_x}, {min_y}) -> ({max_x}, {max_y})")

    def remove_control(self, name: str) -> bool:
        """
        删除控件

        Args:
            name: 控件名称

        Returns:
            bool: 删除是否成功
        """
        if name in self.controls:
            del self.controls[name]
            print(f"已删除控件 '{name}'")
            return True
        else:
            print(f"控件 '{name}' 不存在")
            return False

    def list_controls(self) -> None:
        """列出所有控件信息"""
        if not self.controls:
            print("当前没有配置任何控件")
            return

        print("\n=== 控件列表 ===")
        for name, config in self.controls.items():
            desc = f" - {config['description']}" if config['description'] else ""
            print(f"{name}: ({config['x1']}, {config['y1']}) -> ({config['x2']}, {config['y2']}){desc}")

    def get_random_point_in_control(self, name: str) -> Optional[Tuple[int, int]]:
        """
        获取控件内的随机坐标点

        Args:
            name: 控件名称

        Returns:
            Tuple[int, int]: 随机坐标点 (x, y)，如果控件不存在返回 None
        """
        if name not in self.controls:
            print(f"控件 '{name}' 不存在")
            return None

        control = self.controls[name]
        random_x = random.randint(control['x1'], control['x2'])
        random_y = random.randint(control['y1'], control['y2'])

        return (random_x, random_y)

    def click_control(self, name: str, duration: float = 0.3, delay_before: float = 0, delay_after: float = 0) -> bool:
        """
        随机点击指定控件

        Args:
            name: 控件名称
            duration: 鼠标移动持续时间
            delay_before: 点击前延迟时间
            delay_after: 点击后延迟时间

        Returns:
            bool: 点击是否成功
        """
        point = self.get_random_point_in_control(name)
        if point is None:
            return False

        x, y = point

        try:
            if delay_before > 0:
                time.sleep(delay_before)

            print(f"点击控件 '{name}' 坐标: ({x}, {y})")
            pyautogui.moveTo(x, y, duration=duration)
            pyautogui.click()

            if delay_after > 0:
                time.sleep(delay_after)

            return True
        except Exception as e:
            print(f"点击失败: {e}")
            return False

    def click_control_multiple(self, name: str, count: int, interval: float = 1.0, duration: float = 0.3) -> int:
        """
        多次随机点击指定控件

        Args:
            name: 控件名称
            count: 点击次数
            interval: 点击间隔时间
            duration: 鼠标移动持续时间

        Returns:
            int: 成功点击的次数
        """
        success_count = 0
        for i in range(count):
            if self.click_control(name, duration=duration):
                success_count += 1

            if i < count - 1:  # 最后一次不需要等待
                time.sleep(interval)

        print(f"完成多次点击，成功 {success_count}/{count} 次")
        return success_count


def wait_and_click():
    """保留原有方法以兼容"""
    pyautogui.moveTo(2631, 24, duration=0.3)
    pyautogui.click()
    return False


def main():
    """主函数 - 演示用法"""
    # 创建控件点击器实例
    clicker = ControlClicker()

    # 示例：添加一些控件（你需要根据实际情况修改坐标）
    clicker.add_control("按钮1", 100, 100, 200, 150, "主要按钮")
    clicker.add_control("按钮2", 300, 200, 400, 250, "次要按钮")
    clicker.add_control("输入框", 500, 300, 700, 330, "文本输入区域")

    # 保存配置
    clicker.save_controls_config()

    # 列出所有控件
    clicker.list_controls()

    # 演示点击
    print("\n=== 开始演示点击 ===")
    time.sleep(2)

    # 点击按钮1
    clicker.click_control("按钮1", delay_before=0.5, delay_after=0.5)

    # 多次点击按钮2
    clicker.click_control_multiple("按钮2", count=3, interval=1.0)


if __name__ == "__main__":
    main()
