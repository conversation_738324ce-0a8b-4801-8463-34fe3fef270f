/**
 * Node.js 系统级点击方案
 * 使用 robotjs 实现系统级鼠标控制
 */

const robot = require('robotjs');
const fs = require('fs').promises;

class SystemClicker {
    constructor() {
        this.controls = {};
        // 设置鼠标移动速度（越小越快）
        robot.setMouseDelay(2);
    }

    /**
     * 加载控件配置
     */
    async loadControls(configFile = 'system_controls.json') {
        try {
            const data = await fs.readFile(configFile, 'utf8');
            this.controls = JSON.parse(data);
            console.log(`已加载 ${Object.keys(this.controls).length} 个控件配置`);
        } catch (error) {
            console.log('配置文件不存在，创建新配置');
            this.controls = {};
        }
    }

    /**
     * 保存控件配置
     */
    async saveControls(configFile = 'system_controls.json') {
        await fs.writeFile(configFile, JSON.stringify(this.controls, null, 2));
        console.log('配置已保存');
    }

    /**
     * 添加控件
     */
    addControl(name, x1, y1, x2, y2, description = '') {
        this.controls[name] = { x1, y1, x2, y2, description };
        console.log(`已添加控件 '${name}': (${x1}, ${y1}) -> (${x2}, ${y2})`);
    }

    /**
     * 生成随机延迟
     */
    randomDelay(min = 100, max = 500) {
        return Math.random() * (max - min) + min;
    }

    /**
     * 生成随机坐标
     */
    getRandomPoint(x1, y1, x2, y2) {
        const x = Math.floor(Math.random() * (x2 - x1) + x1);
        const y = Math.floor(Math.random() * (y2 - y1) + y1);
        return { x, y };
    }

    /**
     * 模拟人类鼠标移动
     */
    async humanMouseMove(targetX, targetY) {
        const currentPos = robot.getMousePos();
        const startX = currentPos.x;
        const startY = currentPos.y;

        const distance = Math.sqrt(Math.pow(targetX - startX, 2) + Math.pow(targetY - startY, 2));
        const steps = Math.max(5, Math.floor(distance / 50)); // 根据距离调整步数

        for (let i = 1; i <= steps; i++) {
            const progress = i / steps;
            
            // 使用贝塞尔曲线模拟自然移动
            const t = progress;
            const x = startX + (targetX - startX) * t + (Math.random() - 0.5) * 3;
            const y = startY + (targetY - startY) * t + (Math.random() - 0.5) * 3;

            robot.moveMouse(Math.floor(x), Math.floor(y));
            
            // 随机延迟
            await this.sleep(this.randomDelay(5, 20));
        }

        // 最终移动到精确位置
        robot.moveMouse(targetX, targetY);
    }

    /**
     * 睡眠函数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 点击控件
     */
    async clickControl(name, options = {}) {
        if (!(name in this.controls)) {
            console.log(`控件 '${name}' 不存在`);
            return false;
        }

        const control = this.controls[name];
        const point = this.getRandomPoint(control.x1, control.y1, control.x2, control.y2);

        try {
            // 延迟（如果指定）
            if (options.delayBefore) {
                await this.sleep(options.delayBefore);
            }

            // 模拟人类移动
            await this.humanMouseMove(point.x, point.y);

            // 随机延迟
            await this.sleep(this.randomDelay(50, 200));

            // 点击
            robot.mouseClick();

            console.log(`已点击控件 '${name}' 坐标: (${point.x}, ${point.y})`);

            // 延迟（如果指定）
            if (options.delayAfter) {
                await this.sleep(options.delayAfter);
            }

            return true;

        } catch (error) {
            console.error(`点击失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 多次点击控件
     */
    async clickControlMultiple(name, count, minInterval = 1000, maxInterval = 3000) {
        let successCount = 0;

        for (let i = 0; i < count; i++) {
            if (await this.clickControl(name)) {
                successCount++;
            }

            if (i < count - 1) {
                const interval = this.randomDelay(minInterval, maxInterval);
                console.log(`等待 ${interval.toFixed(0)}ms...`);
                await this.sleep(interval);
            }
        }

        console.log(`完成多次点击，成功 ${successCount}/${count} 次`);
        return successCount;
    }

    /**
     * 获取当前鼠标位置
     */
    getCurrentMousePos() {
        return robot.getMousePos();
    }

    /**
     * 获取屏幕尺寸
     */
    getScreenSize() {
        return robot.getScreenSize();
    }

    /**
     * 截屏
     */
    takeScreenshot(path = 'screenshot.png') {
        const img = robot.screen.capture();
        const width = img.width;
        const height = img.height;
        const bytesPerPixel = img.bytesPerPixel;

        console.log(`截屏完成: ${width}x${height}, ${bytesPerPixel} bytes per pixel`);
        
        // 这里可以保存图片，需要额外的图片处理库
        return img;
    }

    /**
     * 获取像素颜色
     */
    getPixelColor(x, y) {
        return robot.getPixelColor(x, y);
    }

    /**
     * 等待像素颜色变化（可用于等待页面加载）
     */
    async waitForColorChange(x, y, originalColor, timeout = 5000) {
        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            const currentColor = robot.getPixelColor(x, y);
            if (currentColor !== originalColor) {
                return true;
            }
            await this.sleep(100);
        }
        
        return false;
    }

    /**
     * 列出所有控件
     */
    listControls() {
        console.log('\n=== 控件列表 ===');
        for (const [name, config] of Object.entries(this.controls)) {
            console.log(`${name}: (${config.x1}, ${config.y1}) -> (${config.x2}, ${config.y2}) - ${config.description}`);
        }
    }

    /**
     * 删除控件
     */
    removeControl(name) {
        if (name in this.controls) {
            delete this.controls[name];
            console.log(`已删除控件 '${name}'`);
            return true;
        } else {
            console.log(`控件 '${name}' 不存在`);
            return false;
        }
    }
}

// 使用示例
async function example() {
    const clicker = new SystemClicker();
    
    try {
        // 加载配置
        await clicker.loadControls();
        
        // 添加控件
        clicker.addControl('测试按钮', 100, 100, 200, 150, '测试用按钮');
        clicker.addControl('菜单', 50, 20, 150, 60, '主菜单');
        
        // 列出控件
        clicker.listControls();
        
        // 获取当前鼠标位置
        console.log('当前鼠标位置:', clicker.getCurrentMousePos());
        
        // 获取屏幕尺寸
        console.log('屏幕尺寸:', clicker.getScreenSize());
        
        // 等待3秒后开始点击
        console.log('3秒后开始点击...');
        await clicker.sleep(3000);
        
        // 点击测试按钮
        await clicker.clickControl('测试按钮', {
            delayBefore: 500,
            delayAfter: 1000
        });
        
        // 多次点击菜单
        await clicker.clickControlMultiple('菜单', 3, 1000, 2000);
        
        // 保存配置
        await clicker.saveControls();
        
    } catch (error) {
        console.error('执行失败:', error);
    }
}

module.exports = SystemClicker;

// 如果直接运行此文件，执行示例
if (require.main === module) {
    example();
}
