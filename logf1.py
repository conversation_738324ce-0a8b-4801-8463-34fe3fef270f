import asyncio
from playwright.async_api import async_playwright
import time
import csv

async def fetch_data():
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        # 使用已保存的登录状态
        context = await browser.new_context(storage_state="ck.json")
        page = await context.new_page()

        # await page.goto("https://www.3kytmh.vip")
        await page.goto("https://www.3kytmh.vip/game/detail/lottery?enName=YBCP&venueTitle=华体会彩票")
        # element = await page.wait_for_selector(".b2oLamnt4GaMZc__VZPccw__")
        # if element:
        #     # 获取元素内容
        #     text = await element.inner_text()
        #     print(f"找到元素，内容为: {text}")
        #     # 或者获取HTML
        #     html = await element.inner_html()
        #     print(f"元素HTML: {html}")

        await page.wait_for_selector("ul.miss-list li")

    # 获取所有li元素
        items = await page.query_selector_all("ul.miss-list > li")
        for li in items:
            # 彩种名
            name = await li.query_selector(".list-title .list-name")
            name_text = (await name.inner_text()).strip() if name else ""
            # 玩法
            play = await li.query_selector(".list-title .list-play")
            play_text = (await play.inner_text()).strip() if play else ""
            # 连出期数
            lianchu = await li.query_selector(".list-num em")
            lianchu_text = (await lianchu.inner_text()).strip() if lianchu else ""
            # 倒计时
            hour = await li.query_selector(".scounter .hour")
            minute = await li.query_selector(".scounter .minute")
            second = await li.query_selector(".scounter .second")
            time_str = ""
            if hour and minute and second:
                time_str = f"{(await hour.inner_text()).strip()}:{(await minute.inner_text()).strip()}:{(await second.inner_text()).strip()}"

            print(f"彩种: {name_text} | 玩法: {play_text} | 连出: {lianchu_text}期 | 倒计时: {time_str}")


        await browser.close()

asyncio.run(fetch_data())
