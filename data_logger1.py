import uiautomator2 as u2
from PIL import Image, ImageEnhance
import numpy as np
import time
import os
import io
from paddleocr import PaddleOCR
import cv2
import re

# 初始化 PaddleOCR
ocr_engine = PaddleOCR(
    use_textline_orientation=True,
    lang='ch'
)

def preprocess_image(img):
    """优化预处理：只做放大和轻微锐化"""
    img = img.resize((img.width * 2, img.height * 2), Image.LANCZOS)
    img = ImageEnhance.Sharpness(img).enhance(1.3)
    return np.array(img)

def ocr_image(img):
    """对图像执行 OCR 并返回识别结果"""
    img_np = preprocess_image(img)
    return ocr_engine.ocr(img_np)

def screenshot_and_ocr(d, img_path):
    start_time = time.time()
    d.screenshot(img_path)
    img = Image.open(img_path).convert("RGB")
    result = ocr_image(img)
    ocr_data = extract_ocr_data(result)
    end_time = time.time()
    print(f"截图+OCR处理耗时: {end_time - start_time:.2f} 秒")
    return extract_lianchu_triplets_from_result(ocr_data)

def extract_lianchu_triplets_from_result(result):
    texts = [item["text"] for item in result]
    triplets = []
    pattern = re.compile(r"连出\d+期")  # 连出 + 数字 + 期
    for i in range(2, len(texts)):
        if pattern.fullmatch(texts[i].strip()):
            caizhong = texts[i - 2].strip()
            if '00' in caizhong:
                caizhong = texts[i - 3].strip()
            weizhi_val = texts[i - 1].strip()
            lianchu = texts[i].strip()
            triplets.append((caizhong, weizhi_val, lianchu))
    return triplets

def extract_ocr_data(result):
    """从OCR结果中提取文字、位置、置信度"""
    all_data = []
    for res in result:
        if isinstance(res, dict):
            texts = res.get("rec_texts", [])
            scores = res.get("rec_scores", [])
            boxes = res.get("rec_boxes", [])
            for txt, score, box in zip(texts, scores, boxes):
                all_data.append({
                    "text": txt,
                    "score": score,
                    "box": box.tolist() if hasattr(box, 'tolist') else box
                })
    return all_data

def slide_until_bottom_and_capture(d, save_dir="screenshots"):
    """滑动到底部并识别所有连出数据"""
    os.makedirs(save_dir, exist_ok=True)
    all_texts = []
    lianchu_set = set()  # 用于存储已识别的连出数据
    screenshot_index = 0
    start_time = time.time()
    while True:
        text = screenshot_and_ocr(d, os.path.join(save_dir, f"page_{screenshot_index}.png"))
        if not text or (text and text[-1] in lianchu_set):
            print("🚩 已无新数据或最后一条已存在，终止滑动。")
            break
        # 过滤并添加新识别到的连出数据
        new_items = [item for item in text if item not in lianchu_set]
        if new_items:
            all_texts.extend(new_items)
            lianchu_set.update(new_items)
        print(f"📄 第{screenshot_index + 1}页，新增{len(new_items)}条记录")
        d.swipe_ext("up", scale=0.8)
        time.sleep(0.15)
        screenshot_index += 1
    # 回滑至顶部
    for _ in range(screenshot_index - 1):
        d.swipe_ext("down", scale=0.8)
    end_time = time.time()
    print(f"数据处理耗时: {end_time - start_time:.2f} 秒")
    return all_texts

def main():
    d = u2.connect()
    print("✅ 已连接设备:", d.device_info["model"])
    while True:
        print("\n--- 开始新一轮截图与数据处理 ---")
        total_start = time.time()
        all_texts = slide_until_bottom_and_capture(d)
        total_end = time.time()
        print("\n🎯 所有连出数据如下：")
        for i, text in enumerate(all_texts):
            print(f"{i + 1}. {text}")
        print(f"本轮总耗时: {total_end - total_start:.2f} 秒")
        print("等待60秒后进入下一轮...\n")
        time.sleep(60)

if __name__ == "__main__":
    main()
