# 🧪 人工验证测试指南

## 🚀 运行模式

### 1. **默认模式**（推荐）
```bash
node test_stealth.js
```
- 关键步骤会暂停等待你验证
- 自动测试 3 个网站
- 平衡了自动化和人工验证

### 2. **手动模式**（完全控制）
```bash
node test_stealth.js manual
```
- 每个步骤都会等待你的确认
- 完全由你控制测试节奏
- 适合详细验证每个环节

### 3. **快速测试**（单网站）
```bash
node test_stealth.js quick
```
- 仅测试百度网站
- 包含人工验证环节
- 适合快速验证功能

### 4. **仅验证模式**（无自动化）
```bash
node test_stealth.js verify
```
- 打开检测网站但不执行自动化操作
- 纯人工验证防检测效果
- 适合深度检查

## 🔍 人工验证步骤

### 当看到暂停提示时：

```
⏸️  页面已加载完成，请手动验证防检测效果。
你可以：
  1. 打开开发者工具 (F12)
  2. 在控制台输入: navigator.webdriver
  3. 检查是否返回 undefined
  4. 验证其他检测项目
按 Enter 继续自动化测试...
```

### 🛠️ 详细验证方法

#### 1. **基础检测验证**
打开开发者工具 (F12)，在控制台输入：

```javascript
// 检查 webdriver 特征
console.log('webdriver:', navigator.webdriver);  // 应该是 undefined

// 检查 chrome 对象
console.log('chrome:', !!window.chrome);  // 应该是 true

// 检查插件
console.log('plugins:', navigator.plugins.length);  // 应该 > 0

// 检查语言
console.log('languages:', navigator.languages);  // 应该有多个语言
```

#### 2. **高级检测验证**
```javascript
// 检查用户代理
console.log('userAgent:', navigator.userAgent);

// 检查平台信息
console.log('platform:', navigator.platform);

// 检查硬件信息
console.log('hardwareConcurrency:', navigator.hardwareConcurrency);
console.log('deviceMemory:', navigator.deviceMemory);
```

#### 3. **专业检测网站验证**
当访问 `https://bot.sannysoft.com` 时：
- 查看页面上的检测结果
- 绿色 ✅ 表示通过检测
- 红色 ❌ 表示被识别为机器人

## 📊 预期验证结果

### ✅ **成功的验证结果**
```
📊 反检测测试结果:
总体评分: 100.0% (7/7 通过)
  ✅ 通过 - navigator.webdriver 检测
  ✅ 通过 - window.chrome 对象检测
  ✅ 通过 - navigator.permissions 检测
  ✅ 通过 - 浏览器插件检测
  ✅ 通过 - 语言设置检测
  ✅ 通过 - User-Agent 检测
  ✅ 通过 - 平台信息检测
```

### ⚠️ **需要注意的情况**
如果某些检测项显示 ❌，可能原因：
1. 浏览器版本不兼容
2. 某些安全软件干扰
3. 网络环境特殊

## 🎯 验证重点

### 1. **百度测试**
- 验证基础防检测功能
- 检查页面是否正常加载
- 确认没有验证码或异常提示

### 2. **httpbin.org 测试**
- 查看请求头信息
- 确认 User-Agent 正常
- 检查没有自动化相关标识

### 3. **bot.sannysoft.com 测试**
- 这是专业的机器人检测网站
- 重点查看检测结果页面
- 目标是所有项目都显示绿色 ✅

## 💡 验证技巧

### 1. **对比测试**
```bash
# 先运行正常浏览器，记录检测结果
# 再运行我们的脚本，对比差异
```

### 2. **多次测试**
```bash
# 运行多次确保稳定性
node test_stealth.js quick
# 等待几分钟后再次运行
node test_stealth.js quick
```

### 3. **不同网站测试**
```bash
# 可以修改测试脚本添加更多网站
# 或者手动导航到其他网站验证
```

## 🚨 常见问题

### Q: 浏览器启动失败
**A:** 确保已安装依赖：
```bash
npm install puppeteer-extra puppeteer-extra-plugin-stealth
```

### Q: 某些检测项失败
**A:** 尝试：
1. 重启脚本
2. 清理浏览器缓存
3. 检查网络连接

### Q: 想要测试自己的网站
**A:** 修改 `test_stealth.js` 中的网站列表，或者：
```javascript
// 在浏览器打开后，手动导航到目标网站
// 然后进行验证
```

## 📝 验证记录

建议记录验证结果：

| 网站 | 检测评分 | webdriver | chrome | plugins | 备注 |
|------|----------|-----------|---------|---------|------|
| 百度 | 100% | ✅ | ✅ | ✅ | 正常 |
| httpbin | 100% | ✅ | ✅ | ✅ | 正常 |
| bot.sannysoft | 95% | ✅ | ✅ | ⚠️ | 个别项目 |

这样可以跟踪不同网站的检测效果，优化防检测策略。
