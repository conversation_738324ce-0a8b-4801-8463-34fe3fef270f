from device_control import connect_device, get_latest_result_text
from lottery_parser import update_lianlong_status
from data_logger import save_result
import time

def main_loop():
    d = connect_device()
    lianlong_count = 0
    last_result = None

    while True:
        result = get_latest_result_text(d)

        if result != last_result:
            print(f"新开奖：{result}")
            lianlong_count = update_lianlong_status(result, last_result, lianlong_count)
            save_result(result, lianlong_count)
            last_result = result

        time.sleep(3)

if __name__ == "__main__":
    main_loop()
