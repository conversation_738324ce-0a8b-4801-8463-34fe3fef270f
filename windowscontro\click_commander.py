#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
外部指令接口 - 用于接收实时点击指令
"""

import sys
import json
from controlw import ControlClicker


class ClickCommander:
    """点击指令处理器"""
    
    def __init__(self):
        self.clicker = ControlClicker()
        self.running = True
    
    def show_help(self):
        """显示帮助信息"""
        help_text = """
=== 点击指令帮助 ===
可用指令：
  click <控件名>                    - 点击指定控件
  multi <控件名> <次数> [间隔]      - 多次点击控件
  add <控件名> <x1> <y1> <x2> <y2> [描述] - 添加新控件
  remove <控件名>                  - 删除控件
  list                            - 列出所有控件
  save                            - 保存配置
  reload                          - 重新加载配置
  help                            - 显示此帮助
  exit                            - 退出程序

示例：
  click 登录按钮
  multi 按钮1 5 1.5
  add 新按钮 100 100 200 150 这是一个新按钮
  remove 旧按钮
        """
        print(help_text)
    
    def process_command(self, command: str) -> bool:
        """
        处理单个指令
        
        Args:
            command: 用户输入的指令
            
        Returns:
            bool: 是否继续运行
        """
        parts = command.strip().split()
        if not parts:
            return True
        
        cmd = parts[0].lower()
        
        try:
            if cmd == "exit" or cmd == "quit":
                print("退出程序")
                return False
            
            elif cmd == "help" or cmd == "h":
                self.show_help()
            
            elif cmd == "list" or cmd == "ls":
                self.clicker.list_controls()
            
            elif cmd == "save":
                self.clicker.save_controls_config()
            
            elif cmd == "reload":
                self.clicker.load_controls_config()
            
            elif cmd == "click":
                if len(parts) < 2:
                    print("用法: click <控件名>")
                    return True
                
                control_name = " ".join(parts[1:])  # 支持带空格的控件名
                success = self.clicker.click_control(control_name)
                if not success:
                    print(f"点击控件 '{control_name}' 失败")
            
            elif cmd == "multi":
                if len(parts) < 3:
                    print("用法: multi <控件名> <次数> [间隔秒数]")
                    return True
                
                try:
                    control_name = parts[1]
                    count = int(parts[2])
                    interval = float(parts[3]) if len(parts) > 3 else 1.0
                    
                    self.clicker.click_control_multiple(control_name, count, interval)
                except ValueError:
                    print("次数和间隔必须是数字")
            
            elif cmd == "add":
                if len(parts) < 6:
                    print("用法: add <控件名> <x1> <y1> <x2> <y2> [描述]")
                    return True
                
                try:
                    name = parts[1]
                    x1, y1, x2, y2 = map(int, parts[2:6])
                    description = " ".join(parts[6:]) if len(parts) > 6 else ""
                    
                    self.clicker.add_control(name, x1, y1, x2, y2, description)
                except ValueError:
                    print("坐标必须是整数")
            
            elif cmd == "remove" or cmd == "rm":
                if len(parts) < 2:
                    print("用法: remove <控件名>")
                    return True
                
                control_name = " ".join(parts[1:])
                self.clicker.remove_control(control_name)
            
            else:
                print(f"未知指令: {cmd}")
                print("输入 'help' 查看可用指令")
        
        except Exception as e:
            print(f"执行指令时出错: {e}")
        
        return True
    
    def run_interactive(self):
        """运行交互式命令行界面"""
        print("=== 控件点击指令系统 ===")
        print("输入 'help' 查看可用指令，输入 'exit' 退出")
        
        while self.running:
            try:
                command = input("\n> ").strip()
                if not self.process_command(command):
                    break
            except KeyboardInterrupt:
                print("\n\n程序被中断，退出...")
                break
            except EOFError:
                print("\n程序结束")
                break
    
    def run_batch(self, commands_file: str):
        """批量执行指令文件"""
        try:
            with open(commands_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line or line.startswith('#'):  # 跳过空行和注释
                        continue
                    
                    print(f"执行第 {line_num} 行: {line}")
                    if not self.process_command(line):
                        break
        except FileNotFoundError:
            print(f"文件 {commands_file} 不存在")
        except Exception as e:
            print(f"读取文件时出错: {e}")


def main():
    """主函数"""
    commander = ClickCommander()
    
    if len(sys.argv) > 1:
        # 批量模式
        commands_file = sys.argv[1]
        commander.run_batch(commands_file)
    else:
        # 交互模式
        commander.run_interactive()


if __name__ == "__main__":
    main()
