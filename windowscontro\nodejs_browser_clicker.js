/**
 * Node.js 浏览器自动化点击方案
 * 使用 Puppeteer 实现更隐蔽的点击
 */

const puppeteer = require('puppeteer');
const fs = require('fs').promises;

class BrowserClicker {
    constructor() {
        this.browser = null;
        this.page = null;
        this.controls = {};
    }

    /**
     * 初始化浏览器
     */
    async init(options = {}) {
        this.browser = await puppeteer.launch({
            headless: false, // 显示浏览器窗口
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-blink-features=AutomationControlled', // 隐藏自动化特征
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ],
            ...options
        });

        this.page = await this.browser.newPage();
        
        // 隐藏 webdriver 特征
        await this.page.evaluateOnNewDocument(() => {
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
        });

        // 设置用户代理
        await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        
        return this.page;
    }

    /**
     * 加载控件配置
     */
    async loadControls(configFile = 'browser_controls.json') {
        try {
            const data = await fs.readFile(configFile, 'utf8');
            this.controls = JSON.parse(data);
            console.log(`已加载 ${Object.keys(this.controls).length} 个控件配置`);
        } catch (error) {
            console.log('配置文件不存在，创建新配置');
            this.controls = {};
        }
    }

    /**
     * 保存控件配置
     */
    async saveControls(configFile = 'browser_controls.json') {
        await fs.writeFile(configFile, JSON.stringify(this.controls, null, 2));
        console.log('配置已保存');
    }

    /**
     * 添加控件（基于CSS选择器）
     */
    addControl(name, selector, description = '') {
        this.controls[name] = {
            selector,
            description,
            type: 'css'
        };
        console.log(`已添加控件 '${name}': ${selector}`);
    }

    /**
     * 添加坐标控件
     */
    addCoordinateControl(name, x1, y1, x2, y2, description = '') {
        this.controls[name] = {
            x1, y1, x2, y2,
            description,
            type: 'coordinate'
        };
        console.log(`已添加坐标控件 '${name}': (${x1}, ${y1}) -> (${x2}, ${y2})`);
    }

    /**
     * 生成随机延迟（模拟人类行为）
     */
    randomDelay(min = 100, max = 500) {
        return Math.random() * (max - min) + min;
    }

    /**
     * 生成随机坐标
     */
    getRandomPoint(x1, y1, x2, y2) {
        const x = Math.random() * (x2 - x1) + x1;
        const y = Math.random() * (y2 - y1) + y1;
        return { x, y };
    }

    /**
     * 模拟人类鼠标移动轨迹
     */
    async humanMouseMove(x, y) {
        // 获取当前鼠标位置
        const currentPos = await this.page.evaluate(() => {
            return { x: window.mouseX || 0, y: window.mouseY || 0 };
        });

        // 计算移动路径
        const steps = Math.floor(Math.random() * 10) + 5;
        const deltaX = (x - currentPos.x) / steps;
        const deltaY = (y - currentPos.y) / steps;

        // 分步移动，添加随机抖动
        for (let i = 0; i < steps; i++) {
            const currentX = currentPos.x + deltaX * i + (Math.random() - 0.5) * 2;
            const currentY = currentPos.y + deltaY * i + (Math.random() - 0.5) * 2;
            
            await this.page.mouse.move(currentX, currentY);
            await this.page.waitForTimeout(this.randomDelay(10, 50));
        }

        // 最终移动到目标位置
        await this.page.mouse.move(x, y);
    }

    /**
     * 点击CSS选择器控件
     */
    async clickSelector(selector, options = {}) {
        try {
            const element = await this.page.$(selector);
            if (!element) {
                console.log(`未找到元素: ${selector}`);
                return false;
            }

            // 获取元素位置
            const box = await element.boundingBox();
            if (!box) {
                console.log(`无法获取元素位置: ${selector}`);
                return false;
            }

            // 在元素范围内随机选择点击位置
            const clickX = box.x + Math.random() * box.width;
            const clickY = box.y + Math.random() * box.height;

            // 模拟人类移动
            await this.humanMouseMove(clickX, clickY);
            
            // 随机延迟
            await this.page.waitForTimeout(this.randomDelay(50, 200));

            // 点击
            await this.page.mouse.click(clickX, clickY, {
                delay: this.randomDelay(50, 150) // 按下和释放之间的延迟
            });

            console.log(`已点击元素: ${selector} at (${clickX.toFixed(1)}, ${clickY.toFixed(1)})`);
            return true;

        } catch (error) {
            console.error(`点击失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 点击坐标控件
     */
    async clickCoordinate(x1, y1, x2, y2, options = {}) {
        try {
            const point = this.getRandomPoint(x1, y1, x2, y2);
            
            // 模拟人类移动
            await this.humanMouseMove(point.x, point.y);
            
            // 随机延迟
            await this.page.waitForTimeout(this.randomDelay(50, 200));

            // 点击
            await this.page.mouse.click(point.x, point.y, {
                delay: this.randomDelay(50, 150)
            });

            console.log(`已点击坐标: (${point.x.toFixed(1)}, ${point.y.toFixed(1)})`);
            return true;

        } catch (error) {
            console.error(`坐标点击失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 点击控件
     */
    async clickControl(name, options = {}) {
        if (!(name in this.controls)) {
            console.log(`控件 '${name}' 不存在`);
            return false;
        }

        const control = this.controls[name];
        
        if (control.type === 'css') {
            return await this.clickSelector(control.selector, options);
        } else if (control.type === 'coordinate') {
            return await this.clickCoordinate(control.x1, control.y1, control.x2, control.y2, options);
        }

        return false;
    }

    /**
     * 多次点击控件
     */
    async clickControlMultiple(name, count, minInterval = 1000, maxInterval = 3000) {
        let successCount = 0;
        
        for (let i = 0; i < count; i++) {
            if (await this.clickControl(name)) {
                successCount++;
            }
            
            if (i < count - 1) {
                const interval = this.randomDelay(minInterval, maxInterval);
                console.log(`等待 ${interval.toFixed(0)}ms...`);
                await this.page.waitForTimeout(interval);
            }
        }

        console.log(`完成多次点击，成功 ${successCount}/${count} 次`);
        return successCount;
    }

    /**
     * 导航到页面
     */
    async goto(url) {
        await this.page.goto(url, { waitUntil: 'networkidle2' });
    }

    /**
     * 等待元素出现
     */
    async waitForElement(selector, timeout = 5000) {
        try {
            await this.page.waitForSelector(selector, { timeout });
            return true;
        } catch (error) {
            console.log(`等待元素超时: ${selector}`);
            return false;
        }
    }

    /**
     * 关闭浏览器
     */
    async close() {
        if (this.browser) {
            await this.browser.close();
        }
    }

    /**
     * 列出所有控件
     */
    listControls() {
        console.log('\n=== 控件列表 ===');
        for (const [name, config] of Object.entries(this.controls)) {
            if (config.type === 'css') {
                console.log(`${name}: ${config.selector} - ${config.description}`);
            } else {
                console.log(`${name}: (${config.x1}, ${config.y1}) -> (${config.x2}, ${config.y2}) - ${config.description}`);
            }
        }
    }
}

// 使用示例
async function example() {
    const clicker = new BrowserClicker();
    
    try {
        // 初始化浏览器
        await clicker.init();
        
        // 加载配置
        await clicker.loadControls();
        
        // 添加控件
        clicker.addControl('搜索按钮', 'button[type="submit"]', '搜索提交按钮');
        clicker.addControl('输入框', 'input[type="text"]', '搜索输入框');
        clicker.addCoordinateControl('自定义区域', 100, 100, 300, 200, '自定义点击区域');
        
        // 导航到页面
        await clicker.goto('https://www.google.com');
        
        // 等待页面加载
        await clicker.waitForElement('input[name="q"]');
        
        // 点击搜索框
        await clicker.clickSelector('input[name="q"]');
        
        // 输入文本
        await clicker.page.type('input[name="q"]', 'Node.js automation', {
            delay: clicker.randomDelay(50, 150) // 模拟真实打字速度
        });
        
        // 点击搜索按钮
        await clicker.clickSelector('input[value="Google 搜索"]');
        
        // 保存配置
        await clicker.saveControls();
        
        // 等待一段时间后关闭
        setTimeout(async () => {
            await clicker.close();
        }, 5000);
        
    } catch (error) {
        console.error('执行失败:', error);
        await clicker.close();
    }
}

module.exports = BrowserClicker;

// 如果直接运行此文件，执行示例
if (require.main === module) {
    example();
}
