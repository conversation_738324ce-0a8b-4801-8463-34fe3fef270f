# shallow-clone [![NPM version](https://badge.fury.io/js/shallow-clone.svg)](http://badge.fury.io/js/shallow-clone)

> Make a shallow clone of an object, array or primitive.

## Install

Install with [npm](https://www.npmjs.com/)

```sh
$ npm i shallow-clone --save
```

## Usage

```js
var clone = require('shallow-clone');
```

## shallow clones arrays

The array itself is cloned, but not the elements of the array. So any objects in the array will still not be cloned (e.g. they will be the same object as in the orginal array).

```js
var arr = [{ 'a': 0 }, { 'b': 1 }]
var foo = clone(arr);
// foo =>  [{ 'a': 0 }, { 'b': 1 }]

// array is cloned
assert.equal(actual === expected, false);

// array elements are not
assert.deepEqual(actual[0], expected[0]); // true
```

## returns primitives as-is

```js
clone(0)
//=> 0

clone('foo')
//=> 'foo'
```

## shallow clone a regex

```js
clone(/foo/g)
//=> /foo/g
```

## shallow clone an object

```js
clone({a: 1, b: 2, c: 3 })
//=> {a: 1, b: 2, c: 3 }
```

## Related projects

* [assign-deep](https://github.com/jonschlinkert/assign-deep): Deeply assign the enumerable properties of source objects to a destination object.
* [clone-deep](https://github.com/jonschlinkert/clone-deep): Recursively (deep) clone JavaScript native types, like Object, Array, RegExp, Date as well as primitives.
* [extend-shallow](https://github.com/jonschlinkert/extend-shallow): Extend an object with the properties of additional objects. node.js/javascript util.
* [is-plain-object](https://github.com/jonschlinkert/is-plain-object): Returns true if an object was created by the `Object` constructor.
* [mixin-object](https://github.com/jonschlinkert/mixin-object): Mixin the own and inherited properties of other objects onto the first object. Pass an… [more](https://github.com/jonschlinkert/mixin-object)
* [mixin-deep](https://github.com/jonschlinkert/mixin-deep): Deeply mix the properties of objects into the first object. Like merge-deep, but doesn't clone.

## Running tests

Install dev dependencies:

```sh
$ npm i -d && npm test
```

## Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](https://github.com/jonschlinkert/shallow-clone/issues/new)

## Author

**Jon Schlinkert**

+ [github/jonschlinkert](https://github.com/jonschlinkert)
+ [twitter/jonschlinkert](http://twitter.com/jonschlinkert)

## License

Copyright © 2015 Jon Schlinkert
Released under the MIT license.

***

_This file was generated by [verb-cli](https://github.com/assemble/verb-cli) on August 10, 2015._