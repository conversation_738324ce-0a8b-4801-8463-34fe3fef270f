import uiautomator2 as u2
import time

print("准备连接设备...")
d = u2.connect('192.168.137.29:44569')
# d = u2.connect() *************
print("设备已连接")

def dump_ui_elements():
    all_texts = set()
    last_xml = None
    while True:
    d.dump_hierarchy(compressed=False)
    nodes = d.xpath("//*").all()
        for node in nodes:
            text = node.attrib.get('text')
            if text:
                all_texts.add(text)
        # 判断是否到底部（页面结构不再变化）
        xml = d.dump_hierarchy()
        if xml == last_xml:
            break
        last_xml = xml
        d.swipe_ext("up", scale=0.8)
        time.sleep(0.2)
    print(f"\n当前页面控件数：{len(all_texts)}")
    for i, text in enumerate(all_texts):
        print(f"[{i}] text='{text}'")

    with open('page.xml', 'w', encoding='utf-8') as f:
        f.write(xml)

if __name__ == "__main__":
    dump_ui_elements()