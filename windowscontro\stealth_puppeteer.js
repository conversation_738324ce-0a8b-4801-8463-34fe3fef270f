/**
 * 完全防检测的 Puppeteer 方案
 * 使用 puppeteer-extra 和 stealth 插件
 */

const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const fs = require('fs').promises;

// 使用 stealth 插件
puppeteer.use(StealthPlugin());

class StealthClicker {
    constructor() {
        this.browser = null;
        this.page = null;
        this.controls = {};
        this.humanBehavior = {
            mouseHistory: [],
            clickHistory: [],
            typingSpeed: { min: 80, max: 200 }
        };
    }

    /**
     * 初始化完全隐蔽的浏览器
     */
    async init(options = {}) {
        this.browser = await puppeteer.launch({
            headless: false,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
                '--disable-ipc-flooding-protection',
                '--window-size=1366,768'
            ],
            defaultViewport: null,
            ...options
        });

        this.page = await this.browser.newPage();
        
        // 完全隐藏自动化特征
        await this.setupStealthMode();
        
        return this.page;
    }

    /**
     * 设置完全隐蔽模式
     */
    async setupStealthMode() {
        // 1. 完全移除 webdriver 痕迹
        await this.page.evaluateOnNewDocument(() => {
            // 删除 webdriver 属性
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            // 删除自动化相关属性
            delete navigator.__proto__.webdriver;
            delete window.navigator.webdriver;
            
            // 重写 chrome 对象
            window.chrome = {
                app: {
                    isInstalled: false,
                    InstallState: {
                        DISABLED: 'disabled',
                        INSTALLED: 'installed',
                        NOT_INSTALLED: 'not_installed'
                    },
                    RunningState: {
                        CANNOT_RUN: 'cannot_run',
                        READY_TO_RUN: 'ready_to_run',
                        RUNNING: 'running'
                    }
                },
                runtime: {
                    onConnect: null,
                    onMessage: null,
                    onStartup: null,
                    onInstalled: null,
                    onSuspend: null,
                    onSuspendCanceled: null,
                    reload: () => {},
                    requestUpdateCheck: () => {},
                    restart: () => {},
                    restartAfterDelay: () => {},
                    connect: () => {},
                    sendMessage: () => {},
                    getManifest: () => ({
                        name: 'Chrome',
                        version: '91.0.4472.124'
                    })
                },
                csi: () => {},
                loadTimes: () => ({
                    requestTime: Date.now() / 1000,
                    startLoadTime: Date.now() / 1000,
                    commitLoadTime: Date.now() / 1000,
                    finishDocumentLoadTime: Date.now() / 1000,
                    finishLoadTime: Date.now() / 1000,
                    firstPaintTime: Date.now() / 1000,
                    firstPaintAfterLoadTime: 0,
                    navigationType: 'Other'
                })
            };

            // 模拟真实的插件列表
            Object.defineProperty(navigator, 'plugins', {
                get: () => [
                    {
                        0: {type: "application/x-google-chrome-pdf", suffixes: "pdf", description: "Portable Document Format", enabledPlugin: Plugin},
                        description: "Portable Document Format",
                        filename: "internal-pdf-viewer",
                        length: 1,
                        name: "Chrome PDF Plugin"
                    },
                    {
                        0: {type: "application/pdf", suffixes: "pdf", description: "", enabledPlugin: Plugin},
                        description: "",
                        filename: "mhjfbmdgcfjbbpaeojofohoefgiehjai",
                        length: 1,
                        name: "Chrome PDF Viewer"
                    }
                ]
            });

            // 模拟真实的语言设置
            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en-US', 'en']
            });

            // 重写 permissions API
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );

            // 隐藏 headless 特征
            Object.defineProperty(navigator, 'platform', {
                get: () => 'Win32'
            });

            // 模拟真实的内存信息
            Object.defineProperty(navigator, 'deviceMemory', {
                get: () => 8
            });

            // 模拟真实的硬件并发
            Object.defineProperty(navigator, 'hardwareConcurrency', {
                get: () => 4
            });
        });

        // 2. 设置真实的用户代理
        await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');

        // 3. 设置真实的视口
        await this.page.setViewport({
            width: 1366,
            height: 768,
            deviceScaleFactor: 1,
            hasTouch: false,
            isLandscape: true,
            isMobile: false
        });

        // 4. 设置真实的时区
        await this.page.emulateTimezone('Asia/Shanghai');

        // 5. 设置真实的地理位置
        await this.page.setGeolocation({
            latitude: 39.9042,
            longitude: 116.4074,
            accuracy: 100
        });

        // 6. 注入人类行为模拟脚本
        await this.page.evaluateOnNewDocument(() => {
            // 记录真实的鼠标移动
            let mouseEvents = [];
            let lastMouseMove = 0;
            
            document.addEventListener('mousemove', (e) => {
                const now = Date.now();
                if (now - lastMouseMove > 16) { // 限制频率
                    mouseEvents.push({
                        x: e.clientX,
                        y: e.clientY,
                        time: now
                    });
                    lastMouseMove = now;
                    
                    // 保持最近100个事件
                    if (mouseEvents.length > 100) {
                        mouseEvents.shift();
                    }
                }
            });

            // 添加随机的鼠标微动
            setInterval(() => {
                if (Math.random() < 0.1) { // 10% 概率
                    const event = new MouseEvent('mousemove', {
                        clientX: Math.random() * window.innerWidth,
                        clientY: Math.random() * window.innerHeight,
                        bubbles: true
                    });
                    document.dispatchEvent(event);
                }
            }, 5000 + Math.random() * 10000);

            // 模拟真实的焦点变化
            let focusEvents = 0;
            window.addEventListener('focus', () => focusEvents++);
            window.addEventListener('blur', () => focusEvents++);

            // 存储行为数据供检测
            window.humanBehaviorData = {
                mouseEvents: () => mouseEvents,
                focusEvents: () => focusEvents,
                startTime: Date.now()
            };
        });
    }

    /**
     * 生成完全自然的鼠标移动轨迹
     */
    generateNaturalPath(startX, startY, endX, endY) {
        const points = [];
        const distance = Math.sqrt(Math.pow(endX - startX, 2) + Math.pow(endY - startY, 2));
        
        // 根据距离调整步数
        const steps = Math.max(10, Math.min(50, Math.floor(distance / 20)));
        
        // 生成多个控制点创建更自然的路径
        const controlPoints = [];
        const numControls = Math.floor(distance / 200) + 1;
        
        for (let i = 1; i < numControls; i++) {
            const t = i / numControls;
            const baseX = startX + (endX - startX) * t;
            const baseY = startY + (endY - startY) * t;
            
            // 添加随机偏移
            const offsetX = (Math.random() - 0.5) * Math.min(100, distance * 0.2);
            const offsetY = (Math.random() - 0.5) * Math.min(100, distance * 0.2);
            
            controlPoints.push({
                x: baseX + offsetX,
                y: baseY + offsetY
            });
        }
        
        // 使用贝塞尔曲线生成平滑路径
        for (let i = 0; i <= steps; i++) {
            const t = i / steps;
            let x = startX;
            let y = startY;
            
            if (controlPoints.length > 0) {
                // 多点贝塞尔曲线
                const allPoints = [{ x: startX, y: startY }, ...controlPoints, { x: endX, y: endY }];
                const result = this.calculateBezierPoint(t, allPoints);
                x = result.x;
                y = result.y;
            } else {
                // 简单线性插值
                x = startX + (endX - startX) * t;
                y = startY + (endY - startY) * t;
            }
            
            // 添加微小的随机抖动
            x += (Math.random() - 0.5) * 2;
            y += (Math.random() - 0.5) * 2;
            
            points.push({ x: Math.round(x), y: Math.round(y) });
        }
        
        return points;
    }

    /**
     * 计算贝塞尔曲线上的点
     */
    calculateBezierPoint(t, points) {
        if (points.length === 1) return points[0];
        
        const newPoints = [];
        for (let i = 0; i < points.length - 1; i++) {
            const x = points[i].x + (points[i + 1].x - points[i].x) * t;
            const y = points[i].y + (points[i + 1].y - points[i].y) * t;
            newPoints.push({ x, y });
        }
        
        return this.calculateBezierPoint(t, newPoints);
    }

    /**
     * 完全人类化的点击
     */
    async humanClick(x, y, options = {}) {
        try {
            // 1. 获取当前鼠标位置
            const currentPos = await this.page.evaluate(() => {
                return window.humanBehaviorData ? 
                    { x: window.mouseX || window.innerWidth/2, y: window.mouseY || window.innerHeight/2 } :
                    { x: window.innerWidth/2, y: window.innerHeight/2 };
            });

            // 2. 生成自然移动路径
            const path = this.generateNaturalPath(currentPos.x, currentPos.y, x, y);

            // 3. 执行自然移动
            for (let i = 0; i < path.length; i++) {
                const point = path[i];
                await this.page.mouse.move(point.x, point.y);
                
                // 变化的移动速度
                const speed = 5 + Math.random() * 15;
                await this.sleep(speed);
            }

            // 4. 到达目标后的微调
            await this.sleep(50 + Math.random() * 100);
            
            // 5. 模拟真实的点击行为
            const clickDelay = 80 + Math.random() * 120;
            
            await this.page.mouse.down({ button: 'left' });
            await this.sleep(clickDelay);
            await this.page.mouse.up({ button: 'left' });

            // 6. 点击后的微小移动
            const microMoveX = x + (Math.random() - 0.5) * 4;
            const microMoveY = y + (Math.random() - 0.5) * 4;
            await this.page.mouse.move(microMoveX, microMoveY);

            // 7. 记录鼠标位置
            await this.page.evaluate((x, y) => {
                window.mouseX = x;
                window.mouseY = y;
            }, x, y);

            // 8. 更新行为历史
            this.humanBehavior.clickHistory.push({
                x, y, 
                timestamp: Date.now(),
                delay: clickDelay
            });

            console.log(`人类化点击完成: (${x}, ${y})`);
            return true;

        } catch (error) {
            console.error(`点击失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 智能等待（避免检测）
     */
    async smartWait(minMs = 1000, maxMs = 3000) {
        // 基于历史行为调整等待时间
        const baseDelay = minMs + Math.random() * (maxMs - minMs);
        
        // 模拟人类的不规律停顿
        if (Math.random() < 0.3) {
            const extraDelay = Math.random() * 2000;
            await this.sleep(baseDelay + extraDelay);
        } else {
            await this.sleep(baseDelay);
        }
    }

    /**
     * 模拟人类打字
     */
    async humanType(text, options = {}) {
        const chars = text.split('');
        
        for (let i = 0; i < chars.length; i++) {
            const char = chars[i];
            
            // 变化的打字速度
            const delay = this.humanBehavior.typingSpeed.min + 
                         Math.random() * (this.humanBehavior.typingSpeed.max - this.humanBehavior.typingSpeed.min);
            
            // 偶尔的打字错误和修正
            if (Math.random() < 0.05 && i > 0) {
                await this.page.keyboard.press('Backspace');
                await this.sleep(100 + Math.random() * 200);
            }
            
            await this.page.keyboard.type(char);
            await this.sleep(delay);
        }
    }

    /**
     * 检测页面是否在检测自动化
     */
    async detectAntiBot() {
        const result = await this.page.evaluate(() => {
            const indicators = {
                webdriver: !!navigator.webdriver,
                chrome: !window.chrome,
                permissions: !navigator.permissions,
                plugins: navigator.plugins.length === 0,
                languages: navigator.languages.length === 0
            };
            
            return {
                detected: Object.values(indicators).some(v => v),
                indicators
            };
        });
        
        if (result.detected) {
            console.warn('检测到反爬虫机制:', result.indicators);
        }
        
        return result;
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async close() {
        if (this.browser) {
            await this.browser.close();
        }
    }
}

module.exports = StealthClicker;
