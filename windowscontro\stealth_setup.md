# Puppeteer 完全防检测方案

## 🚀 **安装依赖**

```bash
# 安装核心依赖
npm install puppeteer-extra puppeteer-extra-plugin-stealth

# 如果需要额外的反检测插件
npm install puppeteer-extra-plugin-anonymize-ua
npm install puppeteer-extra-plugin-block-resources
```

## 📦 **package.json 示例**

```json
{
  "name": "stealth-clicker",
  "version": "1.0.0",
  "dependencies": {
    "puppeteer-extra": "^3.3.6",
    "puppeteer-extra-plugin-stealth": "^2.11.2",
    "puppeteer-extra-plugin-anonymize-ua": "^2.4.6",
    "puppeteer-extra-plugin-block-resources": "^2.3.5"
  }
}
```

## 🛡️ **完全防检测使用示例**

```javascript
const StealthClicker = require('./stealth_puppeteer');

async function example() {
    const clicker = new StealthClicker();
    
    try {
        // 初始化隐蔽浏览器
        await clicker.init();
        
        // 检测反爬虫机制
        const detection = await clicker.detectAntiBot();
        console.log('反检测状态:', detection);
        
        // 导航到目标页面
        await clicker.page.goto('https://example.com', {
            waitUntil: 'networkidle2'
        });
        
        // 智能等待
        await clicker.smartWait(2000, 5000);
        
        // 人类化点击
        await clicker.humanClick(300, 200);
        
        // 人类化输入
        await clicker.humanType('Hello World');
        
        // 等待并关闭
        await clicker.smartWait(3000, 6000);
        await clicker.close();
        
    } catch (error) {
        console.error('执行失败:', error);
        await clicker.close();
    }
}

example();
```

## 🔍 **防检测特性详解**

### 1. **完全隐藏自动化特征**
- ✅ 移除 `navigator.webdriver`
- ✅ 伪造 `window.chrome` 对象
- ✅ 模拟真实插件列表
- ✅ 设置真实的用户代理
- ✅ 模拟真实的硬件信息

### 2. **人类行为模拟**
- ✅ 贝塞尔曲线鼠标轨迹
- ✅ 随机移动速度和停顿
- ✅ 微小的鼠标抖动
- ✅ 变化的点击延迟
- ✅ 模拟打字错误和修正

### 3. **环境伪装**
- ✅ 真实的时区和地理位置
- ✅ 模拟焦点变化事件
- ✅ 随机的鼠标微动
- ✅ 真实的屏幕分辨率

## 🎯 **检测难度评估**

| 检测方法 | 防护等级 | 说明 |
|----------|----------|------|
| `navigator.webdriver` | ✅ 完全防护 | 完全移除特征 |
| 鼠标轨迹分析 | ✅ 完全防护 | 贝塞尔曲线 + 随机抖动 |
| 时间间隔检测 | ✅ 完全防护 | 智能随机延迟 |
| 事件 `isTrusted` | ✅ 完全防护 | 真实浏览器事件 |
| Canvas 指纹 | ✅ 完全防护 | Stealth 插件处理 |
| WebGL 指纹 | ✅ 完全防护 | Stealth 插件处理 |
| 字体指纹 | ✅ 完全防护 | Stealth 插件处理 |
| 行为分析 | ⚠️ 高级防护 | 需要长期行为模拟 |

## 🔧 **高级配置选项**

```javascript
// 更强的反检测配置
const advancedOptions = {
    // 浏览器启动参数
    launchOptions: {
        headless: false, // 必须为 false
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-blink-features=AutomationControlled',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--disable-gpu',
            '--window-size=1366,768',
            // 额外的反检测参数
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--disable-ipc-flooding-protection'
        ]
    },
    
    // 人类行为参数
    humanBehavior: {
        mouseSpeed: { min: 5, max: 20 },
        clickDelay: { min: 80, max: 200 },
        typingSpeed: { min: 80, max: 200 },
        waitTime: { min: 1000, max: 5000 },
        errorRate: 0.05 // 5% 打字错误率
    }
};
```
