import uiautomator2 as u2
from PIL import Image, ImageEnhance
import numpy as np
import time
import os
import io
from paddleocr import PaddleOCR
import cv2
import re
import pymysql

# 初始化 PaddleOCR
ocr_engine = PaddleOCR(
    use_angle_cls=True,
    lang='ch',  # 中文模型
    rec_model_dir=None,  # 确保使用默认中文模型
    det_model_dir=None   # 确保使用默认检测模型
)



def preprocess_image(img):
    """优化预处理：放大、灰度、二值化、增强锐度和对比度"""
    img = img.resize((img.width * 2, img.height * 2), Image.LANCZOS)
    img_gray = img.convert("L")
    img_np = np.array(img_gray)

    # 二值化处理
    _, img_bin = cv2.threshold(img_np, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    img_pil = Image.fromarray(img_bin).convert("RGB")

    # 增强锐度 & 对比度
    img_pil = ImageEnhance.Sharpness(img_pil).enhance(2.5)
    img_pil = ImageEnhance.Contrast(img_pil).enhance(2.5)
    return np.array(img_pil)

def ocr_image(img):
    """对图像执行 OCR 并返回识别结果"""
    img_np = preprocess_image(img)
    return ocr_engine.ocr(img_np)

def get_box_center(box):
    """获取OCR box的像素中心点，兼容PaddleOCR四点格式"""
    if isinstance(box[0], (list, tuple)):
        xs = [p[0] for p in box]
        ys = [p[1] for p in box]
        center_x = int(sum(xs) / 4)
        center_y = int(sum(ys) / 4)
        return center_x, center_y
    else:
        # 兼容旧格式
        x = int((box[0] + box[2]) / 2)
        y = int((box[1] + box[3]) / 2)
        return x, y

def find_text_in_image(img_path, template_path="refresh_text_template.png", threshold=0.6):
    """用模板匹配查找'刷新'二字在截图中的中心点"""
    img = cv2.imread(img_path)
    template = cv2.imread(template_path)
    if img is None or template is None:
        print("❌ 图片或模板未找到")
        return None
    res = cv2.matchTemplate(img, template, cv2.TM_CCOEFF_NORMED)
    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(res)
    print(f"模板匹配得分: {max_val:.2f}")
    if max_val < threshold:
        print("❌ 未找到'刷新'二字（匹配度低）")
        return None
    h, w = template.shape[:2]
    center_x = max_loc[0] + w // 2
    center_y = max_loc[1] + h // 2
    return center_x, center_y

def screenshot_and_ocr(d, img_path):
    start_time = time.time()
    d.screenshot(img_path)  # 全屏截图
    img = Image.open(img_path)
    img_crop = img.crop((21, 741, 1056, 2170))  # 这里是图片像素坐标
    img_crop.save(img_path)  # 覆盖或另存
    result = ocr_image(img_crop)
    ocr_data = extract_ocr_data(result)


    # 检测到'域名'，直接在截图中查找'刷新'二字并点击
    if any('域名' in item['text'] for item in ocr_data if 'text' in item):
        print("检测到'切换域名'，在截图中查找'刷新'二字并点击...")
        btn_pos = find_text_in_image(img_path, threshold=0.6)
        if btn_pos:
            x, y = btn_pos
            w, h = img_crop.size
            device_w, device_h = d.window_size()
            x_dev = int(x / w * device_w)
            y_dev = int(y / h * device_h)
            print(f"模板匹配到'刷新'，映射到设备坐标: ({x_dev}, {y_dev})")
            d.click(x_dev, y_dev)
        else:
            print("❌ 未找到'刷新'二字")
    
    end_time = time.time()
    print(f"截图+OCR处理耗时: {end_time - start_time:.2f} 秒")
    return extract_lianchu_triplets_from_result(ocr_data)

def extract_lianchu_triplets_from_result(result):
    texts = [item["text"] for item in result]
    triplets = []
    pattern = re.compile(r"连出\d+期")  # 连出 + 数字 + 期

    for i in range(3, len(texts)):
        if pattern.fullmatch(texts[i].strip()):
            group = texts[i-3:i+1]  # 取本条和前面三条共四条
            caizhong = None
            weizhi_val = None
            lianchu = texts[i].strip()
            for t in group:
                t_strip = t.strip()
                if '-' in t_strip:
                    weizhi_val = t_strip
                elif ':' not in t_strip and t_strip != lianchu:
                    caizhong = t_strip
            # 只有caizhong和weizhi_val都找到才加入
            if caizhong and weizhi_val:
                triplet_indices = (i-3, i-1, i)  # 索引可根据需要调整
                caizhong = caizhong.replace(" ", "") if caizhong else ''
                weizhi_val = weizhi_val.replace(" ", "") if weizhi_val else ''
                triplets.append(((caizhong, weizhi_val, lianchu), triplet_indices))

    return triplets


def extract_ocr_data(result):
    """从OCR结果中提取文字、位置、置信度"""
    all_data = []
    for res in result:
        if isinstance(res, dict):
            texts = res.get("rec_texts", [])
            scores = res.get("rec_scores", [])
            boxes = res.get("rec_boxes", [])
            for txt, score, box in zip(texts, scores, boxes):
                all_data.append({
                    "text": txt,
                    "score": score,
                    "box": box.tolist() if hasattr(box, 'tolist') else box
                })
    return all_data

def save_to_mysql(data_list):
    """
    data_list: [(type, result, lianchu)]，lianchu如'连出5期'
    """
    import datetime
    conn = pymysql.connect(
        host='localhost',
        port=3306,
        user='root',
        password='123456',
        database='data_collection',
        charset='utf8mb4'
    )
    cursor = conn.cursor()
    now = datetime.datetime.now()
    five_min_ago = now - datetime.timedelta(minutes=5)
    # 查询过去五分钟数据
    cursor.execute("""
        SELECT id, type, result, count, return_type FROM cp_data WHERE time >= %s
    """, (five_min_ago,))
    recent = cursor.fetchall()
    # 组装方便对比
    recent_map = {}
    for row in recent:
        # row: (id, type, result, count, return_type)
        key = (row[1], row[2], row[3])
        recent_map[key] = row
    # 只用type+result查找
    type_result_map = {}
    for row in recent:
        key2 = (row[1], row[2])
        if key2 not in type_result_map:
            type_result_map[key2] = []
        type_result_map[key2].append(row)
    # 幸运飞艇时间段判断
    def is_xyft_time(now):
        # 13:10-23:59, 00:00-04:10
        start1 = now.replace(hour=13, minute=10, second=0, microsecond=0)
        end1 = now.replace(hour=23, minute=59, second=59, microsecond=999999)
        start2 = now.replace(hour=0, minute=0, second=0, microsecond=0)
        end2 = now.replace(hour=4, minute=10, second=0, microsecond=0)
        if start1 <= now <= end1:
            return True
        if start2 <= now <= end2:
            return True
        return False
    for item in data_list:
        type_, result_, lianchu = item
        type_ = type_.replace(" ", "") if type_ else ''
        result_ = result_.replace(" ", "") if result_ else ''
        # 提取连出期数字
        m = re.search(r"连出(\d+)期", lianchu)
        if m:
            count_ = int(m.group(1))
        else:
            continue  # 格式不符跳过
        key = (type_, result_, count_)
        key2 = (type_, result_)
        # 1. 完全相同跳过
        if key in recent_map:
            continue
        # 幸运飞艇只在13:10-04:10之间允许入库，且处理方式与其它数据相同
        if type_ == '幸运飞艇' and not is_xyft_time(now):
            continue
        # 2. type+result相同，且历史count=当前count-1，更新return_type=1
        updated = False
        if key2 in type_result_map:
            for row in type_result_map[key2]:
                if row[3] == count_ - 1 and row[4] == 0:
                    cursor.execute("UPDATE cp_data SET return_type=1 WHERE id=%s", (row[0],))
                    updated = True
        # 3. 插入新数据
        cursor.execute(
            "INSERT INTO cp_data(type, result, count, time, return_type) VALUES(%s, %s, %s, %s, %s)",
            (type_, result_, count_, now, 0)
        )
    conn.commit()
    cursor.close()
    conn.close()

def slide_until_bottom_and_capture(d, save_dir="screenshots"):
    """滑动到底部并识别所有连出数据"""
    os.makedirs(save_dir, exist_ok=True)
    all_texts = []
    lianchu_set = set()  # 用于存储已识别的连出数据
    screenshot_index = 0
    start_time = time.time()
    while True:
        img_path = os.path.join(save_dir, f"page_{screenshot_index}.png")
        result = screenshot_and_ocr(d, img_path)
        if not result or (result and result[-1][0] in lianchu_set):
            print("🚩 已无新数据或最后一条已存在，终止滑动。")
            break
        # 过滤并添加新识别到的连出数据
        new_items = [item for item in result if item[0] not in lianchu_set]
        if new_items:
            # 只添加元组数据，不包括索引
            all_texts.extend([item[0] for item in new_items])
            lianchu_set.update([item[0] for item in new_items])
            # 打印出所有新数据在当前截图页面数据上的索引位置
            print(f"\n📊 第{screenshot_index + 1}页新增数据的位置：")
            for i, item in enumerate(new_items):
                data_tuple = item[0]
                # 计算该项是当前页面中的第几条记录（从1开始计数）
                item_position = result.index(item) + 1
                print(f"  {i+1}. {data_tuple} - 本页第{item_position}条记录（共{len(result)}条）")
                
        print(f"📄 第{screenshot_index + 1}页，新增{len(new_items)}条记录")
        d.swipe_ext("up", scale=0.8)
        time.sleep(0.15)
        screenshot_index += 1
    # 回滑至顶部
    for _ in range(screenshot_index):
        d.swipe_ext("down", scale=0.8)
    end_time = time.time()
    print(f"数据处理耗时: {end_time - start_time:.2f} 秒")
    return all_texts

def main():
    import time
    d = u2.connect()
    # d = u2.connect("*************:5555")
    print("✅ 已连接设备:", d.device_info["model"])

    while True:
        start_time = time.time()
        all_texts = slide_until_bottom_and_capture(d)
        print("\n🎯 所有连出数据如下：")
        for i, text in enumerate(all_texts):
            print(f"{i + 1}. {text}")
        # 保存到MySQL
        # all_texts: [(type, result, count)]
        save_to_mysql(all_texts)
        end_time = time.time()
        print(f"本次运行总耗时: {end_time - start_time:.2f} 秒")
        print("等待2分钟后再次运行...\n")
        time.sleep(120)


if __name__ == "__main__":
    main()
