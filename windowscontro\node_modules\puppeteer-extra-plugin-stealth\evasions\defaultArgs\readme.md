## API

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

#### Table of Contents

- [class: Plugin](#class-plugin)

### class: [Plugin](https://github.com/berstend/puppeteer-extra/blob/358246d5cc56bbb8800624128503482b8d7b426a/packages/puppeteer-extra-plugin-stealth/evasions/defaultArgs/index.js#L15-L41)

- `opts` (optional, default `{}`)

**Extends: PuppeteerExtraPlugin**

A CDP driver like puppeteer can make use of various browser launch arguments that are
adversarial to mimicking a regular browser and need to be stripped when launching the browser.

---
