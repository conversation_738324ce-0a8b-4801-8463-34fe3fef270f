# 控件点击系统使用说明

## 功能概述

这个系统可以帮你管理页面控件的坐标范围，并实现随机点击功能。主要特性：

- 🎯 **随机点击**：在控件范围内随机选择坐标点击
- 📝 **配置管理**：通过JSON文件管理控件坐标
- 🔄 **实时指令**：支持交互式和批量指令执行
- 📊 **多次点击**：支持指定次数和间隔的重复点击

## 文件说明

- `controlw.py` - 核心控件点击类
- `click_commander.py` - 外部指令接口
- `controls_config.json` - 控件配置文件
- `example_commands.txt` - 示例指令文件

## 快速开始

### 1. 基本使用

```python
from controlw import ControlClicker

# 创建点击器实例
clicker = ControlClicker()

# 添加控件（名称, x1, y1, x2, y2, 描述）
clicker.add_control("登录按钮", 100, 100, 200, 140, "登录页面按钮")

# 点击控件
clicker.click_control("登录按钮")

# 保存配置
clicker.save_controls_config()
```

### 2. 交互式指令模式

```bash
python click_commander.py
```

进入交互模式后可以使用以下指令：

- `click 控件名` - 点击指定控件
- `multi 控件名 次数 间隔` - 多次点击
- `add 控件名 x1 y1 x2 y2 描述` - 添加控件
- `list` - 列出所有控件
- `save` - 保存配置
- `help` - 显示帮助

### 3. 批量指令模式

```bash
python click_commander.py example_commands.txt
```

## 控件配置格式

控件配置文件 `controls_config.json` 格式：

```json
{
  "控件名称": {
    "x1": 左上角X坐标,
    "y1": 左上角Y坐标,
    "x2": 右下角X坐标,
    "y2": 右下角Y坐标,
    "description": "控件描述"
  }
}
```

## API 参考

### ControlClicker 类

#### 主要方法

- `add_control(name, x1, y1, x2, y2, description)` - 添加控件
- `click_control(name, duration, delay_before, delay_after)` - 点击控件
- `click_control_multiple(name, count, interval, duration)` - 多次点击
- `get_random_point_in_control(name)` - 获取随机坐标
- `list_controls()` - 列出所有控件
- `save_controls_config()` - 保存配置
- `load_controls_config()` - 加载配置

#### 参数说明

- `name`: 控件名称
- `x1, y1`: 控件左上角坐标
- `x2, y2`: 控件右下角坐标
- `duration`: 鼠标移动持续时间（秒）
- `delay_before`: 点击前延迟（秒）
- `delay_after`: 点击后延迟（秒）
- `count`: 点击次数
- `interval`: 点击间隔（秒）

## 使用示例

### 示例1：游戏自动点击

```python
clicker = ControlClicker()

# 添加游戏控件
clicker.add_control("攻击按钮", 800, 600, 900, 650)
clicker.add_control("技能1", 700, 600, 750, 650)
clicker.add_control("技能2", 750, 600, 800, 650)

# 连续攻击
clicker.click_control_multiple("攻击按钮", 10, 0.5)

# 释放技能
clicker.click_control("技能1", delay_before=1.0)
clicker.click_control("技能2", delay_before=0.5)
```

### 示例2：表单自动填写

```python
clicker = ControlClicker()

# 添加表单控件
clicker.add_control("用户名框", 200, 100, 400, 130)
clicker.add_control("密码框", 200, 140, 400, 170)
clicker.add_control("登录按钮", 250, 200, 350, 240)

# 依次点击填写
clicker.click_control("用户名框")
# 这里可以添加输入文本的代码
clicker.click_control("密码框", delay_before=0.5)
# 这里可以添加输入密码的代码
clicker.click_control("登录按钮", delay_before=1.0)
```

## 注意事项

1. **坐标获取**：可以使用截图工具或者 `pyautogui.position()` 获取鼠标当前坐标
2. **屏幕分辨率**：确保坐标适配当前屏幕分辨率
3. **安全点击**：建议设置适当的延迟避免操作过快
4. **权限问题**：某些应用可能需要管理员权限才能点击

## 依赖库

```bash
pip install pyautogui
```

## 故障排除

1. **点击无效**：检查坐标是否正确，目标窗口是否在前台
2. **配置丢失**：确保有写入权限，配置文件格式正确
3. **鼠标移动异常**：检查 `pyautogui` 的安全设置
