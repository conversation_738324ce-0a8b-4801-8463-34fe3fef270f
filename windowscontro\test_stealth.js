/**
 * Puppeteer + Stealth 测试脚本
 * 测试防检测效果
 */

const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');

// 使用 stealth 插件
puppeteer.use(StealthPlugin());

class StealthTester {
    constructor() {
        this.browser = null;
        this.page = null;
    }

    /**
     * 初始化隐蔽浏览器
     */
    async init() {
        console.log('🚀 启动隐蔽浏览器...');
        
        this.browser = await puppeteer.launch({
            headless: false, // 显示浏览器窗口
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--disable-dev-shm-usage',
                '--no-first-run',
                '--no-zygote',
                '--window-size=1366,768',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding'
            ],
            defaultViewport: null
        });

        this.page = await this.browser.newPage();
        
        // 设置完全隐蔽模式
        await this.setupStealthMode();
        
        console.log('✅ 浏览器初始化完成');
        return this.page;
    }

    /**
     * 设置完全隐蔽模式
     */
    async setupStealthMode() {
        console.log('🛡️ 设置防检测模式...');
        
        // 完全移除自动化特征
        await this.page.evaluateOnNewDocument(() => {
            // 删除 webdriver 属性
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            delete navigator.__proto__.webdriver;
            
            // 重写 chrome 对象
            window.chrome = {
                app: { isInstalled: false },
                runtime: {
                    onConnect: null,
                    onMessage: null
                },
                csi: () => {},
                loadTimes: () => ({
                    requestTime: Date.now() / 1000,
                    startLoadTime: Date.now() / 1000,
                    commitLoadTime: Date.now() / 1000,
                    finishDocumentLoadTime: Date.now() / 1000,
                    finishLoadTime: Date.now() / 1000,
                    firstPaintTime: Date.now() / 1000
                })
            };

            // 模拟真实插件
            Object.defineProperty(navigator, 'plugins', {
                get: () => [
                    {
                        name: 'Chrome PDF Plugin',
                        filename: 'internal-pdf-viewer',
                        description: 'Portable Document Format'
                    },
                    {
                        name: 'Chrome PDF Viewer',
                        filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai',
                        description: ''
                    }
                ]
            });

            // 模拟真实语言
            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en-US', 'en']
            });

            // 添加鼠标移动记录
            window.mouseEvents = [];
            document.addEventListener('mousemove', (e) => {
                window.mouseEvents.push({
                    x: e.clientX,
                    y: e.clientY,
                    time: Date.now()
                });
                
                // 保持最近50个事件
                if (window.mouseEvents.length > 50) {
                    window.mouseEvents.shift();
                }
            });
        });

        // 设置真实用户代理
        await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
        
        // 设置真实视口
        await this.page.setViewport({
            width: 1366,
            height: 768,
            deviceScaleFactor: 1
        });

        console.log('✅ 防检测模式设置完成');
    }

    /**
     * 检测当前页面的反爬虫机制
     */
    async detectAntiBot() {
        console.log('🔍 检测反爬虫机制...');
        
        const result = await this.page.evaluate(() => {
            const tests = {
                webdriver: {
                    detected: !!navigator.webdriver,
                    description: 'navigator.webdriver 检测'
                },
                chrome: {
                    detected: !window.chrome,
                    description: 'window.chrome 对象检测'
                },
                permissions: {
                    detected: !navigator.permissions,
                    description: 'navigator.permissions 检测'
                },
                plugins: {
                    detected: navigator.plugins.length === 0,
                    description: '浏览器插件检测'
                },
                languages: {
                    detected: navigator.languages.length === 0,
                    description: '语言设置检测'
                },
                userAgent: {
                    detected: /HeadlessChrome/.test(navigator.userAgent),
                    description: 'User-Agent 检测'
                },
                platform: {
                    detected: navigator.platform === '',
                    description: '平台信息检测'
                }
            };

            const detectedCount = Object.values(tests).filter(test => test.detected).length;
            const totalTests = Object.keys(tests).length;

            return {
                tests,
                detectedCount,
                totalTests,
                score: ((totalTests - detectedCount) / totalTests * 100).toFixed(1)
            };
        });

        console.log('\n📊 反检测测试结果:');
        console.log(`总体评分: ${result.score}% (${result.totalTests - result.detectedCount}/${result.totalTests} 通过)`);
        
        for (const [key, test] of Object.entries(result.tests)) {
            const status = test.detected ? '❌ 被检测' : '✅ 通过';
            console.log(`  ${status} - ${test.description}`);
        }

        return result;
    }

    /**
     * 生成人类化鼠标轨迹
     */
    generateHumanPath(startX, startY, endX, endY) {
        const points = [];
        const distance = Math.sqrt(Math.pow(endX - startX, 2) + Math.pow(endY - startY, 2));
        const steps = Math.max(10, Math.min(30, Math.floor(distance / 25)));

        for (let i = 0; i <= steps; i++) {
            const t = i / steps;
            
            // 使用缓动函数使移动更自然
            const easeT = t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
            
            let x = startX + (endX - startX) * easeT;
            let y = startY + (endY - startY) * easeT;
            
            // 添加随机抖动
            x += (Math.random() - 0.5) * 3;
            y += (Math.random() - 0.5) * 3;
            
            points.push({ x: Math.round(x), y: Math.round(y) });
        }

        return points;
    }

    /**
     * 人类化点击
     */
    async humanClick(x, y) {
        console.log(`🖱️ 人类化点击: (${x}, ${y})`);
        
        // 获取当前鼠标位置
        const currentPos = await this.page.evaluate(() => {
            return { 
                x: window.mouseX || window.innerWidth / 2, 
                y: window.mouseY || window.innerHeight / 2 
            };
        });

        // 生成移动路径
        const path = this.generateHumanPath(currentPos.x, currentPos.y, x, y);

        // 执行移动
        for (const point of path) {
            await this.page.mouse.move(point.x, point.y);
            await this.sleep(5 + Math.random() * 10);
        }

        // 随机延迟
        await this.sleep(50 + Math.random() * 100);

        // 执行点击
        await this.page.mouse.down();
        await this.sleep(80 + Math.random() * 120);
        await this.page.mouse.up();

        // 记录鼠标位置
        await this.page.evaluate((x, y) => {
            window.mouseX = x;
            window.mouseY = y;
        }, x, y);

        console.log('✅ 点击完成');
    }

    /**
     * 等待用户确认
     */
    async waitForUserConfirmation(message = "请验证页面，按 Enter 继续...") {
        console.log(`\n⏸️  ${message}`);

        return new Promise((resolve) => {
            const readline = require('readline');
            const rl = readline.createInterface({
                input: process.stdin,
                output: process.stdout
            });

            rl.question('', () => {
                rl.close();
                console.log('✅ 继续执行...\n');
                resolve();
            });
        });
    }

    /**
     * 测试网站交互
     */
    async testWebsiteInteraction(url, options = {}) {
        console.log(`\n🌐 访问网站: ${url}`);

        try {
            // 导航到网站
            await this.page.goto(url, {
                waitUntil: 'networkidle2',
                timeout: 30000
            });

            console.log('✅ 页面加载完成');

            // 等待页面稳定
            await this.sleep(2000);

            // 检测反爬虫
            await this.detectAntiBot();

            // 如果启用了人工验证，等待用户确认
            if (options.waitForUser) {
                await this.waitForUserConfirmation(
                    `页面已加载完成，请手动验证防检测效果。\n` +
                    `你可以：\n` +
                    `  1. 打开开发者工具 (F12)\n` +
                    `  2. 在控制台输入: navigator.webdriver\n` +
                    `  3. 检查是否返回 undefined\n` +
                    `  4. 验证其他检测项目\n` +
                    `按 Enter 继续自动化测试...`
                );
            }

            // 模拟人类浏览行为
            if (options.skipAutomation !== true) {
                await this.simulateHumanBrowsing();
            }

            return true;

        } catch (error) {
            console.error(`❌ 访问失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 模拟人类浏览行为
     */
    async simulateHumanBrowsing() {
        console.log('\n🤖 模拟人类浏览行为...');

        // 1. 随机滚动
        console.log('📜 模拟滚动...');
        for (let i = 0; i < 3; i++) {
            const scrollY = Math.random() * 500 + 200;
            await this.page.evaluate((y) => {
                window.scrollBy(0, y);
            }, scrollY);
            await this.sleep(1000 + Math.random() * 2000);
        }

        // 2. 随机鼠标移动
        console.log('🖱️ 模拟鼠标移动...');
        for (let i = 0; i < 5; i++) {
            const x = Math.random() * 1000 + 100;
            const y = Math.random() * 600 + 100;
            await this.humanClick(x, y);
            await this.sleep(500 + Math.random() * 1500);
        }

        // 3. 尝试查找并点击链接
        console.log('🔗 查找可点击元素...');
        try {
            const links = await this.page.$$('a, button, input[type="button"], input[type="submit"]');
            if (links.length > 0) {
                const randomLink = links[Math.floor(Math.random() * Math.min(3, links.length))];
                const box = await randomLink.boundingBox();
                
                if (box) {
                    const clickX = box.x + box.width / 2;
                    const clickY = box.y + box.height / 2;
                    await this.humanClick(clickX, clickY);
                    console.log('✅ 点击了一个元素');
                }
            }
        } catch (error) {
            console.log('⚠️ 未找到可点击元素');
        }

        console.log('✅ 人类行为模拟完成');
    }

    /**
     * 睡眠函数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 关闭浏览器
     */
    async close() {
        if (this.browser) {
            console.log('🔚 关闭浏览器...');
            await this.browser.close();
        }
    }
}

/**
 * 主测试函数
 */
async function runTest(options = {}) {
    const tester = new StealthTester();

    try {
        // 初始化
        await tester.init();

        // 测试网站列表
        const testSites = [
            {
                url: 'https://www.baidu.com',
                name: '百度首页',
                waitForUser: true  // 第一个网站等待用户验证
            },
            {
                url: 'https://httpbin.org/headers',
                name: '请求头检查',
                waitForUser: options.manualVerify || false
            },
            {
                url: 'https://bot.sannysoft.com',
                name: '机器人检测网站',
                waitForUser: true  // 重要的检测网站等待用户验证
            }
        ];

        // 依次测试每个网站
        for (let i = 0; i < testSites.length; i++) {
            const site = testSites[i];
            console.log(`\n📍 [${i + 1}/${testSites.length}] 测试 ${site.name}`);

            await tester.testWebsiteInteraction(site.url, {
                waitForUser: site.waitForUser,
                skipAutomation: options.skipAutomation
            });

            // 网站间等待（除了最后一个）
            if (i < testSites.length - 1) {
                if (options.manualVerify) {
                    await tester.waitForUserConfirmation('准备测试下一个网站，按 Enter 继续...');
                } else {
                    console.log('\n⏳ 等待 3 秒后测试下一个网站...');
                    await tester.sleep(3000);
                }
            }
        }

        console.log('\n🎉 所有测试完成！');

        // 最终确认
        if (options.manualVerify) {
            await tester.waitForUserConfirmation('测试完成！按 Enter 关闭浏览器...');
        } else {
            console.log('🔍 浏览器将保持打开 10 秒供您观察...');
            await tester.sleep(10000);
        }

    } catch (error) {
        console.error('❌ 测试失败:', error);
    } finally {
        await tester.close();
    }
}

/**
 * 快速测试函数（单个网站）
 */
async function quickTest(url = 'https://www.baidu.com') {
    const tester = new StealthTester();

    try {
        await tester.init();

        console.log(`\n🚀 快速测试: ${url}`);
        await tester.testWebsiteInteraction(url, {
            waitForUser: true,
            skipAutomation: false
        });

        await tester.waitForUserConfirmation('测试完成！按 Enter 关闭浏览器...');

    } catch (error) {
        console.error('❌ 测试失败:', error);
    } finally {
        await tester.close();
    }
}

/**
 * 仅验证模式（不执行自动化操作）
 */
async function verifyOnlyTest() {
    const tester = new StealthTester();

    try {
        await tester.init();

        const url = 'https://bot.sannysoft.com';
        console.log(`\n🔍 仅验证模式: ${url}`);

        await tester.testWebsiteInteraction(url, {
            waitForUser: true,
            skipAutomation: true  // 跳过自动化操作
        });

        await tester.waitForUserConfirmation('验证完成！按 Enter 关闭浏览器...');

    } catch (error) {
        console.error('❌ 测试失败:', error);
    } finally {
        await tester.close();
    }
}

// 运行测试
if (require.main === module) {
    const args = process.argv.slice(2);
    const mode = args[0] || 'default';

    console.log('🧪 开始 Puppeteer + Stealth 防检测测试\n');

    switch (mode) {
        case 'manual':
        case 'm':
            console.log('📋 手动验证模式 - 每个步骤都会等待您的确认');
            runTest({ manualVerify: true }).catch(console.error);
            break;

        case 'quick':
        case 'q':
            console.log('⚡ 快速测试模式 - 仅测试百度');
            quickTest().catch(console.error);
            break;

        case 'verify':
        case 'v':
            console.log('🔍 仅验证模式 - 不执行自动化操作');
            verifyOnlyTest().catch(console.error);
            break;

        case 'help':
        case 'h':
            console.log(`
📖 使用说明:
  node test_stealth.js          - 默认模式（关键步骤等待确认）
  node test_stealth.js manual   - 手动模式（每步都等待确认）
  node test_stealth.js quick    - 快速测试（仅测试百度）
  node test_stealth.js verify   - 仅验证模式（不执行自动化）
  node test_stealth.js help     - 显示此帮助
            `);
            break;

        default:
            console.log('🎯 默认模式 - 关键步骤会等待您的验证');
            runTest().catch(console.error);
    }
}

module.exports = { StealthTester, runTest, quickTest, verifyOnlyTest };
